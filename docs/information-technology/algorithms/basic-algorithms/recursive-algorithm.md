# 递归算法

## 概念
递归算法是一种直接或间接调用自身来解决问题的算法。它将一个大问题分解为若干个与原问题相似但规模更小的子问题，然后递归地解决这些子问题。当子问题规模小到一定程度，可以直接求解时（即达到基线条件），递归过程便开始逐层返回，最终得到原问题的解。

## 递归的要素
一个正确的递归算法必须包含两个核心部分：

- **基线条件 (Base Case)**：也称为递归出口或终止条件。它定义了问题可以直接求解的最小规模，当满足基线条件时，递归不再继续，函数开始返回值。
- **递归步骤 (Recursive Step)**：也称为递归关系。它描述了如何将原问题分解为一个或多个规模更小的、与原问题具有相同性质的子问题，并调用自身来解决这些子问题。递归步骤必须确保每次调用都向基线条件逼近。

## 特点
- **自相似性**：问题可以被分解为与自身结构相似的子问题。
- **代码简洁**：对于某些问题，递归实现的代码结构可能比迭代实现更简洁、更易于理解，因为它更贴近问题的数学定义或逻辑结构。
- **函数调用开销**：每次递归调用都会产生函数调用的开销（如创建新的栈帧、参数传递等），这可能影响性能。
- **栈空间**：深度递归可能导致栈溢出，因为每次函数调用都需要在调用栈上分配空间。

## 应用场景
递归算法常用于解决以下类型的问题：
- **数学计算**：如阶乘、斐波那契数列、最大公约数等。
- **数据结构操作**：如树的遍历（前序、中序、后序）、图的深度优先搜索 (DFS)。
- **分治算法**：如归并排序、快速排序、二分查找（虽然二分查找也常用迭代实现）。
- **回溯算法**：如解决迷宫问题、N皇后问题等。

## 与迭代算法的比较
| 特性         | 递归算法                                     | 迭代算法                                 |
|--------------|----------------------------------------------|------------------------------------------|
| **基本思想**   | 函数调用自身来解决更小规模的子问题             | 通过循环重复执行代码块                     |
| **状态管理**   | 通过函数调用栈隐式管理状态                   | 通常使用局部变量显式管理状态               |
| **终止条件**   | 递归的基线条件 (Base Case)                   | 循环的终止条件                           |
| **代码结构**   | 通常更简洁，更接近数学定义（对于某些问题）     | 通常更长，但可能更直接易懂（对于某些问题） |
| **性能开销**   | 函数调用开销较大（栈帧创建和销毁），可能导致栈溢出 | 函数调用开销较小，通常效率较高             |
| **适用性**     | 适合解决那些具有自相似结构的问题             | 适合解决那些可以被分解为重复步骤的问题     |

选择递归还是迭代，通常取决于问题的性质、代码的清晰度以及性能要求。许多递归算法都可以用迭代的方式实现，反之亦然，但转换的复杂性会有所不同。

## 经典递归算法实例

### 1. 阶乘计算

阶乘是递归的经典例子，n! = n × (n-1)!

```python
def factorial(n):
    """计算n的阶乘"""
    # 基线条件
    if n <= 1:
        return 1
    # 递归步骤
    return n * factorial(n - 1)

# 示例使用
print(factorial(5))  # 输出: 120
print(factorial(0))  # 输出: 1
```

**执行过程分析**：
```
factorial(5)
= 5 * factorial(4)
= 5 * (4 * factorial(3))
= 5 * (4 * (3 * factorial(2)))
= 5 * (4 * (3 * (2 * factorial(1))))
= 5 * (4 * (3 * (2 * 1)))
= 5 * (4 * (3 * 2))
= 5 * (4 * 6)
= 5 * 24
= 120
```

### 2. 斐波那契数列

斐波那契数列：F(n) = F(n-1) + F(n-2)

```python
def fibonacci_basic(n):
    """基础递归版本（效率较低）"""
    if n <= 1:
        return n
    return fibonacci_basic(n - 1) + fibonacci_basic(n - 2)

def fibonacci_optimized(n, memo={}):
    """优化版本：使用记忆化避免重复计算"""
    if n in memo:
        return memo[n]
    if n <= 1:
        return n
    memo[n] = fibonacci_optimized(n - 1, memo) + fibonacci_optimized(n - 2, memo)
    return memo[n]

# 比较两个版本的性能
import time

def compare_fibonacci():
    n = 35

    # 测试基础版本
    start = time.time()
    result1 = fibonacci_basic(n)
    time1 = time.time() - start

    # 测试优化版本
    start = time.time()
    result2 = fibonacci_optimized(n)
    time2 = time.time() - start

    print(f"fibonacci_basic({n}) = {result1}, 耗时: {time1:.4f}秒")
    print(f"fibonacci_optimized({n}) = {result2}, 耗时: {time2:.4f}秒")

compare_fibonacci()
```

### 3. 汉诺塔问题

经典的递归问题，将n个盘子从源柱移动到目标柱。

```python
def hanoi(n, source, target, auxiliary):
    """
    解决汉诺塔问题
    n: 盘子数量
    source: 源柱子
    target: 目标柱子
    auxiliary: 辅助柱子
    """
    if n == 1:
        print(f"将盘子1从 {source} 移动到 {target}")
        return

    # 将前n-1个盘子从源柱移动到辅助柱
    hanoi(n - 1, source, auxiliary, target)

    # 将最大的盘子从源柱移动到目标柱
    print(f"将盘子{n}从 {source} 移动到 {target}")

    # 将n-1个盘子从辅助柱移动到目标柱
    hanoi(n - 1, auxiliary, target, source)

# 示例：解决3层汉诺塔
print("3层汉诺塔的解决步骤：")
hanoi(3, 'A', 'C', 'B')
```

### 4. 二分查找（递归版本）

```python
def binary_search_recursive(arr, target, left, right):
    """递归实现二分查找"""
    # 基线条件：搜索范围无效
    if left > right:
        return -1

    mid = left + (right - left) // 2

    # 基线条件：找到目标
    if arr[mid] == target:
        return mid

    # 递归步骤：在左半部分或右半部分继续搜索
    if arr[mid] > target:
        return binary_search_recursive(arr, target, left, mid - 1)
    else:
        return binary_search_recursive(arr, target, mid + 1, right)

# 示例使用
numbers = [1, 3, 5, 7, 9, 11, 13, 15]
result = binary_search_recursive(numbers, 7, 0, len(numbers) - 1)
print(f"元素7在数组中的位置: {result}")
```

### 5. 树的遍历

递归在树结构操作中非常常见：

```python
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right

def preorder_traversal(root):
    """前序遍历：根-左-右"""
    if root is None:
        return []

    result = [root.val]
    result.extend(preorder_traversal(root.left))
    result.extend(preorder_traversal(root.right))
    return result

def tree_height(root):
    """计算树的高度"""
    if root is None:
        return 0

    left_height = tree_height(root.left)
    right_height = tree_height(root.right)
    return 1 + max(left_height, right_height)

# 创建示例树并测试
#       1
#      / \
#     2   3
#    / \
#   4   5
root = TreeNode(1)
root.left = TreeNode(2)
root.right = TreeNode(3)
root.left.left = TreeNode(4)
root.left.right = TreeNode(5)

print("前序遍历:", preorder_traversal(root))
print("树的高度:", tree_height(root))
```

## 递归优化技巧

### 1. 尾递归优化

尾递归是指递归调用是函数的最后一个操作：

```python
def factorial_tail_recursive(n, accumulator=1):
    """尾递归版本的阶乘"""
    if n <= 1:
        return accumulator
    return factorial_tail_recursive(n - 1, n * accumulator)

# 虽然Python不会自动优化尾递归，但这种写法更清晰
print(factorial_tail_recursive(5))  # 输出: 120
```

### 2. 记忆化递归

使用缓存避免重复计算：

```python
def memoize(func):
    """记忆化装饰器"""
    cache = {}
    def wrapper(*args):
        if args in cache:
            return cache[args]
        result = func(*args)
        cache[args] = result
        return result
    return wrapper

@memoize
def fibonacci_memo(n):
    """使用装饰器的记忆化斐波那契"""
    if n <= 1:
        return n
    return fibonacci_memo(n - 1) + fibonacci_memo(n - 2)
```

## 递归陷阱与调试

### 1. 栈溢出问题

```python
import sys

def deep_recursion(n):
    """可能导致栈溢出的深递归"""
    if n <= 0:
        return 0
    return 1 + deep_recursion(n - 1)

# 查看递归限制
print(f"Python递归限制: {sys.getrecursionlimit()}")

# 可以适当调整递归限制（谨慎使用）
# sys.setrecursionlimit(2000)
```

### 2. 递归调试技巧

```python
def factorial_debug(n, depth=0):
    """带调试信息的阶乘函数"""
    indent = "  " * depth
    print(f"{indent}factorial_debug({n}) 被调用")

    if n <= 1:
        print(f"{indent}基线条件: 返回 1")
        return 1

    result = n * factorial_debug(n - 1, depth + 1)
    print(f"{indent}factorial_debug({n}) 返回 {result}")
    return result

# 运行调试版本
factorial_debug(4)
```

## 练习题

### 基础练习

1. **数字求和**：编写递归函数计算1到n的和
2. **字符串反转**：使用递归反转字符串
3. **数组求和**：递归计算数组所有元素的和
4. **最大公约数**：使用欧几里得算法递归求最大公约数

### 中级练习

5. **回文检查**：递归判断字符串是否为回文
6. **快速排序**：实现递归版本的快速排序
7. **组合数计算**：递归计算C(n,k) = C(n-1,k-1) + C(n-1,k)
8. **路径计算**：在网格中从左上角到右下角的路径数

### 高级练习

9. **N皇后问题**：使用递归和回溯解决N皇后问题
10. **表达式求值**：递归解析和计算数学表达式
11. **迷宫求解**：使用递归找到迷宫的出路
12. **分治算法**：实现归并排序等分治算法

### 练习题参考答案

```python
# 1. 数字求和
def sum_to_n(n):
    if n <= 0:
        return 0
    return n + sum_to_n(n - 1)

# 2. 字符串反转
def reverse_string(s):
    if len(s) <= 1:
        return s
    return s[-1] + reverse_string(s[:-1])

# 3. 数组求和
def array_sum(arr):
    if not arr:
        return 0
    return arr[0] + array_sum(arr[1:])

# 4. 最大公约数
def gcd(a, b):
    if b == 0:
        return a
    return gcd(b, a % b)

# 测试练习题
print("练习题测试:")
print(f"1到10的和: {sum_to_n(10)}")
print(f"字符串反转: {reverse_string('hello')}")
print(f"数组求和: {array_sum([1, 2, 3, 4, 5])}")
print(f"最大公约数: {gcd(48, 18)}")
```

## 注意事项
- **确保基线条件**：必须有一个或多个明确的基线条件，并且递归过程最终能够达到这些条件，否则会导致无限递归和栈溢出。
- **栈溢出风险**：对于递归深度过大的问题，需要警惕栈溢出的风险。在某些情况下，可以考虑将递归算法转换为迭代算法（尾递归优化有时可以由编译器自动完成，但并非所有语言和情况都支持）。
- **重复计算**：朴素的递归实现可能会导致对相同子问题的重复计算（例如，斐波那契数列的简单递归实现）。这种情况下，可以使用记忆化（Memoization）或动态规划来优化。

通过这些实例和练习，你将深入理解递归算法的精髓，掌握递归思维，为解决复杂问题打下坚实基础。
