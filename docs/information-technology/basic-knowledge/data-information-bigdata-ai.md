---
title: 数据、信息、大数据及人工智能
---

# 数据、信息、大数据及人工智能

## 数据与信息基础

### 数据的定义
数据是对客观事物的符号表示，其表现形式可以是文字(数字)、图形、图像、音频、视频等。

### 信息的定义
信息是数据经过储存、分析及解释后所产生的意义。信息论之父香农说它是用来消除随机不确定的东西。

### 信息的特征
信息的特征包括：
1. **载体依附性**：即信息必须依附于载体存在，如果存储信息的载体遭到破坏，那么其承载的信息也会消失。
2. **时效性**：信息往往反映的是事物某一特定时间内的状态，它会随着时间的推移而变化。
3. **共享性**：信息可以共享，且在传播的过程中不会产生损耗。
4. **可加工处理性、真伪性**：信息是可以进行加工的，加工后的信息具有真伪性。
5. **价值性**：信息的价值包括显性价值和隐形价值两个方面，价值的高低具有相对性。

### 数据安全保障措施
数据加密提高数据的保密性；数据校验保障数据的完整性；存储介质的保护措施有：磁盘阵列、数据备份、异地容灾。

## 大数据

### 大数据特征
大数据特征(4V)为：
- 数量(Volume)大
- 速度(Velocity)快
- 类型(Variety)多
- 价值(Value)密度低

### 大数据思维
大数据思维的核心在于：
1. 大数据要分析的是全体数据，而不是抽样数据。
2. 对于数据不再追求精确性，而是能够接受数据的混杂性。
3. 不一定强调对事物因果关系的探求，而是更加注重它们的相关性。

### 大数据处理思想
大数据处理采用"分治"思想：即将大任务分解为小任务各个击破，然后将结果整合成问题的解。

### 大数据处理类型
大数据处理类型包括：
1. **静态数据**：是在处理时已收集完成的不会改变的数据，一般采用批处理进行处理。
2. **流数据**：是不间断地、持续地到达的实时数据。
3. **图数据**：可以刻画现实中很多复杂关系的数据，如社交网络的朋友关系、道路交通数据。

### Hadoop系统
Hadoop是一个可运行于大规模计算机集群上的分布式系统基础架构，适用于静态数据的批处理计算。其核心组件包括：
1. **分布式文件系统HDFS**：具有高容错性，适合部署在廉价机器上，是云盘、网盘的底层实现。
2. **分布式数据库HBase**：采用基于列的存储方式，主要用来存储非结构化数据和半结构化数据。
3. **分布式并行计算模型MapReduce**：主要由Map(映射)和Reduce(归纳)两个函数构成，能将任务分解并分发到多个节点上并行处理，最后汇总输出。

## 文本数据处理

### 文本处理应用
文本数据处理主要应用在搜索引擎、情报分析、自动摘要、自动校对、论文查重、文本分类、垃圾邮件过滤、机器翻译、自动应答等方面。

### 文本处理流程
文本处理的一般过程是：分词→特征提取→数据分析→结果呈现。

### 中文分词算法
常见的中文分词算法有：
1. **基于词典的分词**：即通过匹配词典中的词语进行分词。
2. **基于统计的分词**：即通过统计上下文词语出现的频率进行分词。
3. **基于规则的分词**：即通过学习和应用预设的中文语法规则进行分词。

### 文本可视化与分析
标签云是用词频来表现文本特征，并通过文字大小的形式直观展示词语重要性的一种可视化方法。文本情感分析是对文本内容所表达的情感倾向（如正面、负面、中性）做出分类判断的技术。

## 数据可视化

### 可视化方法
数据可视化的基本方法有：
1. 表现时间趋势的可以使用柱形图、折线图。
2. 展现各部分的大小及其占总体比例关系的，可以用饼图、环形图(也称面包圈图)。
3. 探究具有2~3个变量关联性的数据的分布关系，可以使用散点图、气泡图等。
4. 探寻包含多种变量的对象与同类之间的差异和联系，可以采用雷达图。

## 人工智能

### 人工智能定义
人工智能是指以机器(计算机)为载体，模仿、延伸和扩展人类智能的技术或学科，它与人类或其他动物所呈现的生物智能有着重要区别，涉及众多学科领域。

### 人工智能方法
人工智能的主要方法包括：
1. **符号主义**：认为智能特征可以被符号精确地描述，从而被机器仿真，其方法包含知识库和推理引擎两部分，典型代表是"专家系统"。
2. **联结主义**：模仿人类大脑中神经元之间的复杂交互来进行认知推理，是一种典型的深度学习模型，不需要事先手工构造知识库，常用于人脸识别、语音识别、自然语言处理、图像分类等（需要海量数据）。
3. **行为主义**：强调智能体在与环境的交互中不断学习，通过试错和反馈调整行为，属于强化学习范畴，典型代表有扫地机器人、AlphaGo Zero（不依赖人类棋手数据，在自我博弈中提高棋力）。

### 人工智能应用类型
人工智能的应用类型包括：
1. **领域人工智能**：依赖于特定领域知识和数据的人工智能。
2. **跨领域人工智能**：指智能系统能够从一个领域学习并应用到另外一个领域。
3. **混合增强人工智能**：它将人的作用引入人工智能系统，使人类智能成为智能回路的关键部分或开关。

### 图灵测试
图灵测试是一种用于测试机器是否具有与人类无法区分的智能的方法。
