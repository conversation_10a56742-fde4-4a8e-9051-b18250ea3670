# 杭高技术学科知识库 (HGTechKB)

浙江省杭州高级中学技术学科知识库，为选考技术学科的学生提供系统化的学习资源，涵盖信息技术与通用技术两大领域的核心知识点。

[![Vercel](https://img.shields.io/badge/Powered%20by-Vercel-black.svg?style=flat-square)](https://vercel.com)

## 项目概述

本知识库是为杭高技术学科选考生精心打造的学习平台，具有以下特点：

- **系统性知识梳理**：提供结构化的信息技术和通用技术知识体系，帮助学生系统学习
- **聚焦核心考点**：内容紧密结合学考要求，突出重点难点，助力学生备考
- **丰富的学习资源**：整合课件、笔记、习题等多种学习资料，满足多样化学习需求

## 主要内容

### 信息技术

- **基础知识**
  - 信息系统、支撑技术与信息安全
  - 数据采集与编码
  - 数据、信息、大数据及人工智能

- **算法**
  - 算法基础概念与效率
  - 基本算法（迭代算法、递归算法）
  - 排序算法（冒泡排序等）
  - 查找算法（顺序查找、二分查找等）

- **Python 编程**
  - Python 基础知识概览
  - Python 基础知识字典
  - 数据结构（数组、字符串、队列、栈、树）

### 通用技术

通用技术部分正在建设中，敬请期待。

## 技术栈

本项目使用 [Rspress](https://rspress.dev/) 构建，这是一个基于 React 的静态站点生成器。

## 开发指南

### 环境准备

安装依赖：

```bash
npm install
# 或者使用 pnpm
pnpm install
```

### 本地开发

启动开发服务器：

```bash
npm run dev
# 或者使用 pnpm
pnpm dev
```

### 构建部署

构建生产版本：

```bash
npm run build
# 或者使用 pnpm
pnpm build
```

本地预览生产构建：

```bash
npm run preview
# 或者使用 pnpm
pnpm preview
```

## 贡献指南

欢迎教师和学生为知识库贡献内容！请遵循以下步骤：

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

## 联系方式

如有任何问题或建议，请联系技术学科组老师。

---

© 2025 浙江省杭州高级中学
